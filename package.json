{"name": "cesium-project", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@stratakit/foundations": "^0.2.4", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@cesium/wasm-splats": "^0.1.0-alpha.2", "@spz-loader/core": "0.3.0", "@tweenjs/tween.js": "^25.0.0", "@vitejs/plugin-vue": "^6.0.1", "@zip.js/zip.js": "^2.7.70", "autolinker": "^4.0.0", "bitmap-sdf": "^1.0.3", "dompurify": "^3.0.2", "draco3d": "^1.5.1", "earcut": "^3.0.0", "grapheme-splitter": "^1.0.4", "jsep": "^1.3.8", "kdbush": "^4.0.1", "ktx-parse": "^1.0.0", "lerc": "^2.0.0", "mersenne-twister": "^1.1.0", "meshoptimizer": "^0.25.0", "nosleep.js": "^0.12.0", "pako": "^2.0.4", "protobufjs": "^7.1.0", "rbush": "^4.0.1", "topojson-client": "^3.1.0", "urijs": "^1.19.7", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0"}}