/**
 * Constants to determine how much time advances with each call
 * to {@link Clock#tick}.
 *
 * @enum {number}
 *
 * @see Clock
 * @see ClockRange
 */
const ClockStep = {
  /**
   * {@link Clock#tick} advances the current time by a fixed step,
   * which is the number of seconds specified by {@link Clock#multiplier}.
   *
   * @type {number}
   * @constant
   */
  TICK_DEPENDENT: 0,

  /**
   * {@link Clock#tick} advances the current time by the amount of system
   * time elapsed since the previous call multiplied by {@link Clock#multiplier}.
   *
   * @type {number}
   * @constant
   */
  SYSTEM_CLOCK_MULTIPLIER: 1,

  /**
   * {@link Clock#tick} sets the clock to the current system time;
   * ignoring all other settings.
   *
   * @type {number}
   * @constant
   */
  SYSTEM_CLOCK: 2,
};
export default Object.freeze(ClockStep);
